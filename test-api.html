<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - SkillUp Lab</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>SkillUp Lab API Test</h1>
    
    <div class="test-section">
        <h2>1. Health Check</h2>
        <button onclick="testHealthCheck()">Test Health Endpoint</button>
        <div id="health-result"></div>
    </div>

    <div class="test-section">
        <h2>2. API Info</h2>
        <button onclick="testAPIInfo()">Test API Info</button>
        <div id="api-result"></div>
    </div>

    <div class="test-section">
        <h2>3. User Registration</h2>
        <button onclick="testRegistration()">Test Registration</button>
        <div id="register-result"></div>
    </div>

    <div class="test-section">
        <h2>4. User Login</h2>
        <button onclick="testLogin()">Test Login</button>
        <div id="login-result"></div>
    </div>

    <div class="test-section">
        <h2>5. Protected Endpoint</h2>
        <button onclick="testProtectedEndpoint()">Test Profile (Protected)</button>
        <div id="profile-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        let authToken = null;

        function showResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            const resultClass = success ? 'success' : 'error';
            let content = `<div class="test-result ${resultClass}">
                <strong>${success ? '✅ Success' : '❌ Error'}:</strong> ${message}
            </div>`;
            
            if (data) {
                content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            element.innerHTML = content;
        }

        async function makeRequest(endpoint, options = {}) {
            try {
                const url = `${API_BASE}${endpoint}`;
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                };

                if (authToken) {
                    config.headers.Authorization = `Bearer ${authToken}`;
                }

                const response = await fetch(url, config);
                const data = await response.json();
                
                return {
                    success: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return {
                    success: false,
                    status: 0,
                    data: { message: error.message }
                };
            }
        }

        async function testHealthCheck() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();
                
                if (response.ok) {
                    showResult('health-result', true, 'Health check passed', data);
                } else {
                    showResult('health-result', false, 'Health check failed', data);
                }
            } catch (error) {
                showResult('health-result', false, `Connection failed: ${error.message}`);
            }
        }

        async function testAPIInfo() {
            const result = await makeRequest('');
            showResult('api-result', result.success, 
                result.success ? 'API info retrieved' : result.data.message, 
                result.data);
        }

        async function testRegistration() {
            const testUser = {
                username: 'testuser_' + Date.now(),
                email: `test_${Date.now()}@example.com`,
                password: 'TestPassword123',
                firstName: 'Test',
                lastName: 'User',
                role: 'student'
            };

            const result = await makeRequest('/auth/register', {
                method: 'POST',
                body: JSON.stringify(testUser)
            });

            if (result.success && result.data.data && result.data.data.tokens) {
                authToken = result.data.data.tokens.accessToken;
                showResult('register-result', true, 'Registration successful! Token saved.', result.data);
            } else {
                showResult('register-result', false, 
                    result.data.message || 'Registration failed', result.data);
            }
        }

        async function testLogin() {
            // First try to register a user, then login
            const testUser = {
                username: 'logintest_' + Date.now(),
                email: `logintest_${Date.now()}@example.com`,
                password: 'TestPassword123',
                firstName: 'Login',
                lastName: 'Test',
                role: 'student'
            };

            // Register first
            await makeRequest('/auth/register', {
                method: 'POST',
                body: JSON.stringify(testUser)
            });

            // Then login
            const loginResult = await makeRequest('/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    username: testUser.username,
                    password: testUser.password
                })
            });

            if (loginResult.success && loginResult.data.data && loginResult.data.data.tokens) {
                authToken = loginResult.data.data.tokens.accessToken;
                showResult('login-result', true, 'Login successful! Token saved.', loginResult.data);
            } else {
                showResult('login-result', false, 
                    loginResult.data.message || 'Login failed', loginResult.data);
            }
        }

        async function testProtectedEndpoint() {
            if (!authToken) {
                showResult('profile-result', false, 'No auth token available. Please register or login first.');
                return;
            }

            const result = await makeRequest('/auth/profile');
            showResult('profile-result', result.success, 
                result.success ? 'Profile retrieved successfully' : result.data.message, 
                result.data);
        }

        // Auto-run health check on page load
        window.addEventListener('load', () => {
            testHealthCheck();
        });
    </script>
</body>
</html>
