{"name": "skillup-lab-backend", "version": "1.0.0", "description": "Node.js/Express backend API for form-based application with role-based access control", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "setup:production": "node database/production-setup.js", "migrate": "node database/migrations.js migrate", "seed": "node database/migrations.js seed", "migrate:seed": "node database/migrations.js migrate:seed", "validate:env": "node -e \"require('./config/config.js'); console.log('✅ Environment configuration is valid')\"", "validate:production": "node scripts/validate-production.js", "security:check": "npm audit --audit-level moderate"}, "keywords": ["express", "nodejs", "api", "rbac", "forms", "mysql", "planetscale"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "mysql2": "^3.14.3"}, "devDependencies": {"jest": "^30.0.5", "nodemon": "^3.1.10", "supertest": "^7.1.4"}}