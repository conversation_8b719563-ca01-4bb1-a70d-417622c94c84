<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teacher Dashboard - SkillUp Lab</title>
    <link rel="stylesheet" href="style.css">
    <link rel="icon" type="image/png" href="images/ChatGPT Image May 5, 2025, 12_54_09 AM.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .dashboard-header {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .dashboard-header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5rem;
        }

        .dashboard-header p {
            margin: 0;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #00b894;
        }

        .dashboard-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .dashboard-card h3 i {
            color: #00b894;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #00b894;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
            margin: 5px;
        }

        .btn:hover {
            background: #00a085;
        }

        .btn-primary {
            background: #74b9ff;
        }

        .btn-primary:hover {
            background: #0984e3;
        }

        .forms-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .form-item {
            padding: 15px;
            border: 1px solid #e1e1e1;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .form-item:hover {
            border-color: #00b894;
            transform: translateY(-2px);
        }

        .form-item h4 {
            margin: 0 0 5px 0;
            color: #333;
        }

        .form-item p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }

        .form-actions {
            display: flex;
            gap: 10px;
        }

        .form-actions button {
            padding: 5px 10px;
            font-size: 12px;
        }

        .form-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-top: 5px;
        }

        .status-published {
            background: #d4edda;
            color: #155724;
        }

        .status-draft {
            background: #fff3cd;
            color: #856404;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #ccc;
        }

        .loading {
            text-align: center;
            padding: 40px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #00b894;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .action-card {
            flex: 1;
            min-width: 200px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s;
        }

        .action-card:hover {
            transform: translateY(-5px);
        }

        .action-card i {
            font-size: 2rem;
            color: #00b894;
            margin-bottom: 10px;
        }

        .action-card h4 {
            margin: 0 0 5px 0;
            color: #333;
        }

        .action-card p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            position: relative;
        }

        .close {
            position: absolute;
            right: 20px;
            top: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #00b894;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <img src="images/bglogo.png" alt="SkillUp Logo">
                <span>SkillUp Lab</span>
            </div>
            <nav class="nav-links">
                <a href="index.html">Home</a>
                <span class="user-info"></span>
                <a href="#" class="logout-btn">Logout</a>
            </nav>
        </div>
    </header>

    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1><i class="fas fa-chalkboard-teacher"></i> Teacher Dashboard</h1>
            <p>Manage your forms and track student progress</p>
        </div>

        <!-- Quick Actions -->
        <div class="dashboard-card">
            <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
            <div class="quick-actions">
                <div class="action-card" onclick="teacherDashboard.showCreateFormModal()">
                    <i class="fas fa-plus-circle"></i>
                    <h4>Create Form</h4>
                    <p>Build a new form for students</p>
                </div>
                <div class="action-card" onclick="teacherDashboard.loadMyForms()">
                    <i class="fas fa-list"></i>
                    <h4>View Forms</h4>
                    <p>Manage your existing forms</p>
                </div>
                <div class="action-card" onclick="teacherDashboard.loadSubmissions()">
                    <i class="fas fa-paper-plane"></i>
                    <h4>Submissions</h4>
                    <p>Review student submissions</p>
                </div>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- My Forms -->
            <div class="dashboard-card">
                <h3><i class="fas fa-clipboard-list"></i> My Forms</h3>
                <div id="myForms" class="forms-list">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>Loading forms...</p>
                    </div>
                </div>
            </div>

            <!-- Recent Submissions -->
            <div class="dashboard-card">
                <h3><i class="fas fa-inbox"></i> Recent Submissions</h3>
                <div id="recentSubmissions" class="forms-list">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>Loading submissions...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form Modal -->
    <div id="createFormModal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Create New Form</h2>
            <form id="createFormForm">
                <div class="form-group">
                    <label for="formTitle">Form Title *</label>
                    <input type="text" id="formTitle" name="title" required>
                </div>
                <div class="form-group">
                    <label for="formDescription">Description</label>
                    <textarea id="formDescription" name="description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="formStatus">Status</label>
                    <select id="formStatus" name="status">
                        <option value="draft">Draft</option>
                        <option value="published">Published</option>
                    </select>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button type="submit" class="btn btn-primary">Create Form</button>
                    <button type="button" class="btn" onclick="document.getElementById('createFormModal').style.display='none'">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script>
        class TeacherDashboard {
            constructor() {
                this.api = window.skillUpAPI;
                this.authManager = window.authManager;
                this.init();
            }

            init() {
                // Check authentication
                if (!this.authManager.requireAuth(['teacher', 'admin'])) {
                    return;
                }

                this.loadDashboardData();
                this.setupEventListeners();
            }

            setupEventListeners() {
                // Modal close
                const modal = document.getElementById('createFormModal');
                const closeBtn = modal.querySelector('.close');
                
                closeBtn.addEventListener('click', () => {
                    modal.style.display = 'none';
                });

                // Create form submission
                document.getElementById('createFormForm').addEventListener('submit', (e) => {
                    this.handleCreateForm(e);
                });
            }

            async loadDashboardData() {
                await Promise.all([
                    this.loadMyForms(),
                    this.loadSubmissions()
                ]);
            }

            async loadMyForms() {
                try {
                    const response = await this.api.getForms();
                    const container = document.getElementById('myForms');

                    if (response.success && response.data.forms.length > 0) {
                        container.innerHTML = response.data.forms.map(form => `
                            <div class="form-item">
                                <div>
                                    <h4>${form.title}</h4>
                                    <p>${form.description || 'No description'}</p>
                                    <span class="form-status status-${form.status}">${form.status}</span>
                                </div>
                                <div class="form-actions">
                                    <button class="btn" onclick="teacherDashboard.editForm(${form.id})">Edit</button>
                                    <button class="btn" onclick="teacherDashboard.viewSubmissions(${form.id})">Submissions</button>
                                </div>
                            </div>
                        `).join('');
                    } else {
                        container.innerHTML = `
                            <div class="empty-state">
                                <i class="fas fa-clipboard-list"></i>
                                <p>No forms created yet</p>
                                <button class="btn" onclick="teacherDashboard.showCreateFormModal()">Create Your First Form</button>
                            </div>
                        `;
                    }
                } catch (error) {
                    console.error('Error loading forms:', error);
                }
            }

            async loadSubmissions() {
                try {
                    const response = await this.api.getSubmissions();
                    const container = document.getElementById('recentSubmissions');

                    if (response.success && response.data.submissions.length > 0) {
                        container.innerHTML = response.data.submissions.slice(0, 5).map(submission => `
                            <div class="form-item" onclick="teacherDashboard.viewSubmission(${submission.id})">
                                <div>
                                    <h4>${submission.form_title}</h4>
                                    <p>By: ${submission.first_name} ${submission.last_name}</p>
                                    <span class="form-status status-submitted">${submission.status}</span>
                                </div>
                            </div>
                        `).join('');
                    } else {
                        container.innerHTML = `
                            <div class="empty-state">
                                <i class="fas fa-inbox"></i>
                                <p>No submissions yet</p>
                            </div>
                        `;
                    }
                } catch (error) {
                    console.error('Error loading submissions:', error);
                }
            }

            showCreateFormModal() {
                document.getElementById('createFormModal').style.display = 'block';
            }

            async handleCreateForm(event) {
                event.preventDefault();
                
                const form = event.target;
                const formData = new FormData(form);
                
                const formPayload = {
                    title: formData.get('title'),
                    description: formData.get('description'),
                    status: formData.get('status'),
                    fields: [] // Start with empty fields, can be added later
                };

                try {
                    this.authManager.showLoading(true);
                    
                    const response = await this.api.createForm(formPayload);
                    
                    if (response.success) {
                        this.authManager.showMessage('Form created successfully!', 'success');
                        document.getElementById('createFormModal').style.display = 'none';
                        form.reset();
                        
                        // Reload forms
                        await this.loadMyForms();
                    } else {
                        this.authManager.showMessage(response.message || 'Failed to create form', 'error');
                    }
                } catch (error) {
                    console.error('Create form error:', error);
                    this.authManager.showMessage('Failed to create form. Please try again.', 'error');
                } finally {
                    this.authManager.showLoading(false);
                }
            }

            editForm(formId) {
                this.authManager.showMessage('Form editing feature coming soon!', 'info');
            }

            viewSubmissions(formId) {
                this.authManager.showMessage('Viewing submissions for form ' + formId, 'info');
            }

            viewSubmission(submissionId) {
                this.authManager.showMessage('Viewing submission ' + submissionId, 'info');
            }
        }

        // Initialize dashboard when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.teacherDashboard = new TeacherDashboard();
        });
    </script>
</body>
</html>
