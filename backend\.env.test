# Test Environment Configuration
# This file is used for running tests

# Server Configuration
PORT=3001
NODE_ENV=test

# Database Configuration (Use test database)
# You should create a separate test database
DATABASE_URL=mysql://username:<EMAIL>/test_database_name?sslaccept=strict
DB_HOST=host.planetscale.com
DB_USER=username
DB_PASSWORD=password
DB_NAME=test_database_name
DB_PORT=3306

# JWT Configuration (Use different secrets for testing)
JWT_SECRET=test-jwt-secret-key-for-testing-only
JWT_REFRESH_SECRET=test-refresh-secret-key-for-testing-only
JWT_EXPIRE=1h
JWT_REFRESH_EXPIRE=7d

# Rate Limiting (More lenient for testing)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# CORS Configuration
CORS_ORIGIN=http://localhost:3001

# Security
BCRYPT_SALT_ROUNDS=4

# Logging
LOG_LEVEL=error
