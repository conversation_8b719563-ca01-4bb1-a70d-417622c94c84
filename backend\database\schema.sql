-- SkillUp Lab Database Schema
-- Designed for PlanetScale (MySQL compatible)
-- Note: PlanetScale doesn't support foreign key constraints, so we use indexes instead

-- Users table for authentication and role management
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'teacher', 'student') NOT NULL DEFAULT 'student',
    first_name <PERSON><PERSON><PERSON><PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
);

-- Forms table for storing form definitions
CREATE TABLE forms (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    created_by INT NOT NULL,
    status ENUM('draft', 'published', 'archived') NOT NULL DEFAULT 'draft',
    is_active BOOLEAN DEFAULT TRUE,
    settings JSON, -- Store form settings like deadline, max_submissions, etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance (PlanetScale doesn't support FK constraints)
    INDEX idx_created_by (created_by),
    INDEX idx_status (status),
    INDEX idx_active (is_active),
    INDEX idx_created_at (created_at)
);

-- Form fields table for dynamic form structure
CREATE TABLE form_fields (
    id INT AUTO_INCREMENT PRIMARY KEY,
    form_id INT NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    field_label VARCHAR(200) NOT NULL,
    field_type ENUM('text', 'textarea', 'email', 'number', 'select', 'radio', 'checkbox', 'date', 'file') NOT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    field_order INT NOT NULL DEFAULT 0,
    options JSON, -- Store select/radio/checkbox options
    validation_rules JSON, -- Store validation rules (min, max, pattern, etc.)
    placeholder VARCHAR(200),
    help_text TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_form_id (form_id),
    INDEX idx_field_order (field_order),
    INDEX idx_field_type (field_type)
);

-- Form submissions table for storing user responses
CREATE TABLE form_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    form_id INT NOT NULL,
    submitted_by INT NOT NULL,
    submission_data JSON NOT NULL, -- Store all form field responses
    status ENUM('draft', 'submitted', 'reviewed') NOT NULL DEFAULT 'submitted',
    score DECIMAL(5,2) NULL, -- For graded forms
    feedback TEXT,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_form_id (form_id),
    INDEX idx_submitted_by (submitted_by),
    INDEX idx_status (status),
    INDEX idx_submitted_at (submitted_at),
    
    -- Ensure one submission per user per form (can be modified based on requirements)
    UNIQUE KEY unique_user_form (form_id, submitted_by)
);

-- Refresh tokens table for JWT token management
CREATE TABLE refresh_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_user_id (user_id),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at)
);

-- Form permissions table for granular access control
CREATE TABLE form_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    form_id INT NOT NULL,
    user_id INT NOT NULL,
    permission_type ENUM('view', 'submit', 'edit', 'manage') NOT NULL,
    granted_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_form_id (form_id),
    INDEX idx_user_id (user_id),
    INDEX idx_permission_type (permission_type),
    
    -- Ensure unique permission per user per form
    UNIQUE KEY unique_user_form_permission (form_id, user_id, permission_type)
);
