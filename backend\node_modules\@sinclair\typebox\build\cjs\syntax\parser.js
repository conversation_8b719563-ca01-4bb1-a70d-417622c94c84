"use strict";

Object.defineProperty(exports, "__esModule", { value: true });
exports.Constructor = exports.Function = exports.ParameterList = exports.ParameterList_0 = exports.Parameter = exports.Tuple = exports.ElementList = exports.ElementList_0 = exports._Object = exports.PropertyList = exports.PropertyList_0 = exports.PropertyDelimiter = exports.Property = exports.Optional = exports.Readonly = exports.PropertyKey = exports.Type = exports.Expr = exports.ExprTail = exports.ExprTerm = exports.ExprTermTail = exports.Factor = exports.Base = exports.Extends = exports.IndexArray = exports.IndexArray_0 = exports.KeyOf = exports.Literal = exports.LiteralBoolean = exports.LiteralNumber = exports.LiteralString = exports.Keyword = exports.KeywordVoid = exports.KeywordSymbol = exports.KeywordNever = exports.KeywordAny = exports.KeywordUnknown = exports.KeywordBigInt = exports.KeywordInteger = exports.KeywordNull = exports.KeywordUndefined = exports.KeywordBoolean = exports.KeywordNumber = exports.KeywordString = exports.GenericArguments = exports.GenericArgumentsList = exports.GenericArgumentsList_0 = exports.GenericReference = exports.GenericReferenceParameterList = exports.GenericReferenceParameterList_0 = void 0;
exports.Reference = exports.Uint8Array = exports.Date = exports.Uncapitalize = exports.Capitalize = exports.Lowercase = exports.Uppercase = exports.Extract = exports.Exclude = exports.Omit = exports.Pick = exports.Required = exports.Partial = exports.ReturnType = exports.InstanceType = exports.FunctionParameters = exports.ConstructorParameters = exports.Promise = exports.Record = exports.Array = exports.Awaited = exports.Argument = exports.Iterator = exports.AsyncIterator = exports.Mapped = void 0;
const index_1 = require("../parser/index");
const S = require("./mapping");
const If = (result, left, right = () => []) => (result.length === 2 ? left(result) : right());
const GenericReferenceParameterList_0 = (input, context, result = []) => If(If((0, exports.Type)(input, context), ([_0, input]) => If(index_1.Runtime.Token.Const(',', input), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => (0, exports.GenericReferenceParameterList_0)(input, context, [...result, _0]), () => [result, input]);
exports.GenericReferenceParameterList_0 = GenericReferenceParameterList_0;
const GenericReferenceParameterList = (input, context = {}) => If(If((0, exports.GenericReferenceParameterList_0)(input, context), ([_0, input]) => If(If(If((0, exports.Type)(input, context), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [S.GenericReferenceParameterListMapping(_0, context), input]);
exports.GenericReferenceParameterList = GenericReferenceParameterList;
const GenericReference = (input, context = {}) => If(If(index_1.Runtime.Token.Ident(input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.GenericReferenceParameterList)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.GenericReferenceMapping(_0, context), input]);
exports.GenericReference = GenericReference;
const GenericArgumentsList_0 = (input, context, result = []) => If(If(index_1.Runtime.Token.Ident(input), ([_0, input]) => If(index_1.Runtime.Token.Const(',', input), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => (0, exports.GenericArgumentsList_0)(input, context, [...result, _0]), () => [result, input]);
exports.GenericArgumentsList_0 = GenericArgumentsList_0;
const GenericArgumentsList = (input, context = {}) => If(If((0, exports.GenericArgumentsList_0)(input, context), ([_0, input]) => If(If(If(index_1.Runtime.Token.Ident(input), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [S.GenericArgumentsListMapping(_0, context), input]);
exports.GenericArgumentsList = GenericArgumentsList;
const GenericArguments = (input, context = {}) => If(If(index_1.Runtime.Token.Const('<', input), ([_0, input]) => If((0, exports.GenericArgumentsList)(input, context), ([_1, input]) => If(index_1.Runtime.Token.Const('>', input), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [S.GenericArgumentsMapping(_0, context), input]);
exports.GenericArguments = GenericArguments;
const KeywordString = (input, context = {}) => If(index_1.Runtime.Token.Const('string', input), ([_0, input]) => [S.KeywordStringMapping(_0, context), input]);
exports.KeywordString = KeywordString;
const KeywordNumber = (input, context = {}) => If(index_1.Runtime.Token.Const('number', input), ([_0, input]) => [S.KeywordNumberMapping(_0, context), input]);
exports.KeywordNumber = KeywordNumber;
const KeywordBoolean = (input, context = {}) => If(index_1.Runtime.Token.Const('boolean', input), ([_0, input]) => [S.KeywordBooleanMapping(_0, context), input]);
exports.KeywordBoolean = KeywordBoolean;
const KeywordUndefined = (input, context = {}) => If(index_1.Runtime.Token.Const('undefined', input), ([_0, input]) => [S.KeywordUndefinedMapping(_0, context), input]);
exports.KeywordUndefined = KeywordUndefined;
const KeywordNull = (input, context = {}) => If(index_1.Runtime.Token.Const('null', input), ([_0, input]) => [S.KeywordNullMapping(_0, context), input]);
exports.KeywordNull = KeywordNull;
const KeywordInteger = (input, context = {}) => If(index_1.Runtime.Token.Const('integer', input), ([_0, input]) => [S.KeywordIntegerMapping(_0, context), input]);
exports.KeywordInteger = KeywordInteger;
const KeywordBigInt = (input, context = {}) => If(index_1.Runtime.Token.Const('bigint', input), ([_0, input]) => [S.KeywordBigIntMapping(_0, context), input]);
exports.KeywordBigInt = KeywordBigInt;
const KeywordUnknown = (input, context = {}) => If(index_1.Runtime.Token.Const('unknown', input), ([_0, input]) => [S.KeywordUnknownMapping(_0, context), input]);
exports.KeywordUnknown = KeywordUnknown;
const KeywordAny = (input, context = {}) => If(index_1.Runtime.Token.Const('any', input), ([_0, input]) => [S.KeywordAnyMapping(_0, context), input]);
exports.KeywordAny = KeywordAny;
const KeywordNever = (input, context = {}) => If(index_1.Runtime.Token.Const('never', input), ([_0, input]) => [S.KeywordNeverMapping(_0, context), input]);
exports.KeywordNever = KeywordNever;
const KeywordSymbol = (input, context = {}) => If(index_1.Runtime.Token.Const('symbol', input), ([_0, input]) => [S.KeywordSymbolMapping(_0, context), input]);
exports.KeywordSymbol = KeywordSymbol;
const KeywordVoid = (input, context = {}) => If(index_1.Runtime.Token.Const('void', input), ([_0, input]) => [S.KeywordVoidMapping(_0, context), input]);
exports.KeywordVoid = KeywordVoid;
const Keyword = (input, context = {}) => If(If((0, exports.KeywordString)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.KeywordNumber)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.KeywordBoolean)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.KeywordUndefined)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.KeywordNull)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.KeywordInteger)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.KeywordBigInt)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.KeywordUnknown)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.KeywordAny)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.KeywordNever)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.KeywordSymbol)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.KeywordVoid)(input, context), ([_0, input]) => [_0, input], () => [])))))))))))), ([_0, input]) => [S.KeywordMapping(_0, context), input]);
exports.Keyword = Keyword;
const LiteralString = (input, context = {}) => If(index_1.Runtime.Token.String(["'", '"', '`'], input), ([_0, input]) => [S.LiteralStringMapping(_0, context), input]);
exports.LiteralString = LiteralString;
const LiteralNumber = (input, context = {}) => If(index_1.Runtime.Token.Number(input), ([_0, input]) => [S.LiteralNumberMapping(_0, context), input]);
exports.LiteralNumber = LiteralNumber;
const LiteralBoolean = (input, context = {}) => If(If(index_1.Runtime.Token.Const('true', input), ([_0, input]) => [_0, input], () => If(index_1.Runtime.Token.Const('false', input), ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.LiteralBooleanMapping(_0, context), input]);
exports.LiteralBoolean = LiteralBoolean;
const Literal = (input, context = {}) => If(If((0, exports.LiteralBoolean)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.LiteralNumber)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.LiteralString)(input, context), ([_0, input]) => [_0, input], () => []))), ([_0, input]) => [S.LiteralMapping(_0, context), input]);
exports.Literal = Literal;
const KeyOf = (input, context = {}) => If(If(If(index_1.Runtime.Token.Const('keyof', input), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.KeyOfMapping(_0, context), input]);
exports.KeyOf = KeyOf;
const IndexArray_0 = (input, context, result = []) => If(If(If(index_1.Runtime.Token.Const('[', input), ([_0, input]) => If((0, exports.Type)(input, context), ([_1, input]) => If(index_1.Runtime.Token.Const(']', input), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [_0, input], () => If(If(index_1.Runtime.Token.Const('[', input), ([_0, input]) => If(index_1.Runtime.Token.Const(']', input), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [_0, input], () => [])), ([_0, input]) => (0, exports.IndexArray_0)(input, context, [...result, _0]), () => [result, input]);
exports.IndexArray_0 = IndexArray_0;
const IndexArray = (input, context = {}) => If((0, exports.IndexArray_0)(input, context), ([_0, input]) => [S.IndexArrayMapping(_0, context), input]);
exports.IndexArray = IndexArray;
const Extends = (input, context = {}) => If(If(If(index_1.Runtime.Token.Const('extends', input), ([_0, input]) => If((0, exports.Type)(input, context), ([_1, input]) => If(index_1.Runtime.Token.Const('?', input), ([_2, input]) => If((0, exports.Type)(input, context), ([_3, input]) => If(index_1.Runtime.Token.Const(':', input), ([_4, input]) => If((0, exports.Type)(input, context), ([_5, input]) => [[_0, _1, _2, _3, _4, _5], input])))))), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.ExtendsMapping(_0, context), input]);
exports.Extends = Extends;
const Base = (input, context = {}) => If(If(If(index_1.Runtime.Token.Const('(', input), ([_0, input]) => If((0, exports.Type)(input, context), ([_1, input]) => If(index_1.Runtime.Token.Const(')', input), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [_0, input], () => If((0, exports.Keyword)(input, context), ([_0, input]) => [_0, input], () => If((0, exports._Object)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Tuple)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Literal)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Constructor)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Function)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Mapped)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.AsyncIterator)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Iterator)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.ConstructorParameters)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.FunctionParameters)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.InstanceType)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.ReturnType)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Argument)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Awaited)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Array)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Record)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Promise)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Partial)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Required)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Pick)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Omit)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Exclude)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Extract)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Uppercase)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Lowercase)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Capitalize)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Uncapitalize)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Date)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Uint8Array)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.GenericReference)(input, context), ([_0, input]) => [_0, input], () => If((0, exports.Reference)(input, context), ([_0, input]) => [_0, input], () => []))))))))))))))))))))))))))))))))), ([_0, input]) => [S.BaseMapping(_0, context), input]);
exports.Base = Base;
const Factor = (input, context = {}) => If(If((0, exports.KeyOf)(input, context), ([_0, input]) => If((0, exports.Base)(input, context), ([_1, input]) => If((0, exports.IndexArray)(input, context), ([_2, input]) => If((0, exports.Extends)(input, context), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.FactorMapping(_0, context), input]);
exports.Factor = Factor;
const ExprTermTail = (input, context = {}) => If(If(If(index_1.Runtime.Token.Const('&', input), ([_0, input]) => If((0, exports.Factor)(input, context), ([_1, input]) => If((0, exports.ExprTermTail)(input, context), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.ExprTermTailMapping(_0, context), input]);
exports.ExprTermTail = ExprTermTail;
const ExprTerm = (input, context = {}) => If(If((0, exports.Factor)(input, context), ([_0, input]) => If((0, exports.ExprTermTail)(input, context), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [S.ExprTermMapping(_0, context), input]);
exports.ExprTerm = ExprTerm;
const ExprTail = (input, context = {}) => If(If(If(index_1.Runtime.Token.Const('|', input), ([_0, input]) => If((0, exports.ExprTerm)(input, context), ([_1, input]) => If((0, exports.ExprTail)(input, context), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.ExprTailMapping(_0, context), input]);
exports.ExprTail = ExprTail;
const Expr = (input, context = {}) => If(If((0, exports.ExprTerm)(input, context), ([_0, input]) => If((0, exports.ExprTail)(input, context), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [S.ExprMapping(_0, context), input]);
exports.Expr = Expr;
const Type = (input, context = {}) => If(If(If((0, exports.GenericArguments)(input, context), ([_0, input]) => (0, exports.Expr)(input, _0), () => []), ([_0, input]) => [_0, input], () => If((0, exports.Expr)(input, context), ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.TypeMapping(_0, context), input]);
exports.Type = Type;
const PropertyKey = (input, context = {}) => If(If(index_1.Runtime.Token.Ident(input), ([_0, input]) => [_0, input], () => If(index_1.Runtime.Token.String(["'", '"'], input), ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.PropertyKeyMapping(_0, context), input]);
exports.PropertyKey = PropertyKey;
const Readonly = (input, context = {}) => If(If(If(index_1.Runtime.Token.Const('readonly', input), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.ReadonlyMapping(_0, context), input]);
exports.Readonly = Readonly;
const Optional = (input, context = {}) => If(If(If(index_1.Runtime.Token.Const('?', input), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_0, input]) => [S.OptionalMapping(_0, context), input]);
exports.Optional = Optional;
const Property = (input, context = {}) => If(If((0, exports.Readonly)(input, context), ([_0, input]) => If((0, exports.PropertyKey)(input, context), ([_1, input]) => If((0, exports.Optional)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const(':', input), ([_3, input]) => If((0, exports.Type)(input, context), ([_4, input]) => [[_0, _1, _2, _3, _4], input]))))), ([_0, input]) => [S.PropertyMapping(_0, context), input]);
exports.Property = Property;
const PropertyDelimiter = (input, context = {}) => If(If(If(index_1.Runtime.Token.Const(',', input), ([_0, input]) => If(index_1.Runtime.Token.Const('\n', input), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [_0, input], () => If(If(index_1.Runtime.Token.Const(';', input), ([_0, input]) => If(index_1.Runtime.Token.Const('\n', input), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [_0, input], () => If(If(index_1.Runtime.Token.Const(',', input), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If(If(index_1.Runtime.Token.Const(';', input), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If(If(index_1.Runtime.Token.Const('\n', input), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => []))))), ([_0, input]) => [S.PropertyDelimiterMapping(_0, context), input]);
exports.PropertyDelimiter = PropertyDelimiter;
const PropertyList_0 = (input, context, result = []) => If(If((0, exports.Property)(input, context), ([_0, input]) => If((0, exports.PropertyDelimiter)(input, context), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => (0, exports.PropertyList_0)(input, context, [...result, _0]), () => [result, input]);
exports.PropertyList_0 = PropertyList_0;
const PropertyList = (input, context = {}) => If(If((0, exports.PropertyList_0)(input, context), ([_0, input]) => If(If(If((0, exports.Property)(input, context), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [S.PropertyListMapping(_0, context), input]);
exports.PropertyList = PropertyList;
const _Object = (input, context = {}) => If(If(index_1.Runtime.Token.Const('{', input), ([_0, input]) => If((0, exports.PropertyList)(input, context), ([_1, input]) => If(index_1.Runtime.Token.Const('}', input), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [S.ObjectMapping(_0, context), input]);
exports._Object = _Object;
const ElementList_0 = (input, context, result = []) => If(If((0, exports.Type)(input, context), ([_0, input]) => If(index_1.Runtime.Token.Const(',', input), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => (0, exports.ElementList_0)(input, context, [...result, _0]), () => [result, input]);
exports.ElementList_0 = ElementList_0;
const ElementList = (input, context = {}) => If(If((0, exports.ElementList_0)(input, context), ([_0, input]) => If(If(If((0, exports.Type)(input, context), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [S.ElementListMapping(_0, context), input]);
exports.ElementList = ElementList;
const Tuple = (input, context = {}) => If(If(index_1.Runtime.Token.Const('[', input), ([_0, input]) => If((0, exports.ElementList)(input, context), ([_1, input]) => If(index_1.Runtime.Token.Const(']', input), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [S.TupleMapping(_0, context), input]);
exports.Tuple = Tuple;
const Parameter = (input, context = {}) => If(If(index_1.Runtime.Token.Ident(input), ([_0, input]) => If(index_1.Runtime.Token.Const(':', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => [[_0, _1, _2], input]))), ([_0, input]) => [S.ParameterMapping(_0, context), input]);
exports.Parameter = Parameter;
const ParameterList_0 = (input, context, result = []) => If(If((0, exports.Parameter)(input, context), ([_0, input]) => If(index_1.Runtime.Token.Const(',', input), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => (0, exports.ParameterList_0)(input, context, [...result, _0]), () => [result, input]);
exports.ParameterList_0 = ParameterList_0;
const ParameterList = (input, context = {}) => If(If((0, exports.ParameterList_0)(input, context), ([_0, input]) => If(If(If((0, exports.Parameter)(input, context), ([_0, input]) => [[_0], input]), ([_0, input]) => [_0, input], () => If([[], input], ([_0, input]) => [_0, input], () => [])), ([_1, input]) => [[_0, _1], input])), ([_0, input]) => [S.ParameterListMapping(_0, context), input]);
exports.ParameterList = ParameterList;
const Function = (input, context = {}) => If(If(index_1.Runtime.Token.Const('(', input), ([_0, input]) => If((0, exports.ParameterList)(input, context), ([_1, input]) => If(index_1.Runtime.Token.Const(')', input), ([_2, input]) => If(index_1.Runtime.Token.Const('=>', input), ([_3, input]) => If((0, exports.Type)(input, context), ([_4, input]) => [[_0, _1, _2, _3, _4], input]))))), ([_0, input]) => [S.FunctionMapping(_0, context), input]);
exports.Function = Function;
const Constructor = (input, context = {}) => If(If(index_1.Runtime.Token.Const('new', input), ([_0, input]) => If(index_1.Runtime.Token.Const('(', input), ([_1, input]) => If((0, exports.ParameterList)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const(')', input), ([_3, input]) => If(index_1.Runtime.Token.Const('=>', input), ([_4, input]) => If((0, exports.Type)(input, context), ([_5, input]) => [[_0, _1, _2, _3, _4, _5], input])))))), ([_0, input]) => [S.ConstructorMapping(_0, context), input]);
exports.Constructor = Constructor;
const Mapped = (input, context = {}) => If(If(index_1.Runtime.Token.Const('{', input), ([_0, input]) => If(index_1.Runtime.Token.Const('[', input), ([_1, input]) => If(index_1.Runtime.Token.Ident(input), ([_2, input]) => If(index_1.Runtime.Token.Const('in', input), ([_3, input]) => If((0, exports.Type)(input, context), ([_4, input]) => If(index_1.Runtime.Token.Const(']', input), ([_5, input]) => If(index_1.Runtime.Token.Const(':', input), ([_6, input]) => If((0, exports.Type)(input, context), ([_7, input]) => If(index_1.Runtime.Token.Const('}', input), ([_8, input]) => [[_0, _1, _2, _3, _4, _5, _6, _7, _8], input]))))))))), ([_0, input]) => [S.MappedMapping(_0, context), input]);
exports.Mapped = Mapped;
const AsyncIterator = (input, context = {}) => If(If(index_1.Runtime.Token.Const('AsyncIterator', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.AsyncIteratorMapping(_0, context), input]);
exports.AsyncIterator = AsyncIterator;
const Iterator = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Iterator', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.IteratorMapping(_0, context), input]);
exports.Iterator = Iterator;
const Argument = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Argument', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.ArgumentMapping(_0, context), input]);
exports.Argument = Argument;
const Awaited = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Awaited', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.AwaitedMapping(_0, context), input]);
exports.Awaited = Awaited;
const Array = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Array', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.ArrayMapping(_0, context), input]);
exports.Array = Array;
const Record = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Record', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const(',', input), ([_3, input]) => If((0, exports.Type)(input, context), ([_4, input]) => If(index_1.Runtime.Token.Const('>', input), ([_5, input]) => [[_0, _1, _2, _3, _4, _5], input])))))), ([_0, input]) => [S.RecordMapping(_0, context), input]);
exports.Record = Record;
const Promise = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Promise', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.PromiseMapping(_0, context), input]);
exports.Promise = Promise;
const ConstructorParameters = (input, context = {}) => If(If(index_1.Runtime.Token.Const('ConstructorParameters', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.ConstructorParametersMapping(_0, context), input]);
exports.ConstructorParameters = ConstructorParameters;
const FunctionParameters = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Parameters', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.FunctionParametersMapping(_0, context), input]);
exports.FunctionParameters = FunctionParameters;
const InstanceType = (input, context = {}) => If(If(index_1.Runtime.Token.Const('InstanceType', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.InstanceTypeMapping(_0, context), input]);
exports.InstanceType = InstanceType;
const ReturnType = (input, context = {}) => If(If(index_1.Runtime.Token.Const('ReturnType', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.ReturnTypeMapping(_0, context), input]);
exports.ReturnType = ReturnType;
const Partial = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Partial', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.PartialMapping(_0, context), input]);
exports.Partial = Partial;
const Required = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Required', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.RequiredMapping(_0, context), input]);
exports.Required = Required;
const Pick = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Pick', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const(',', input), ([_3, input]) => If((0, exports.Type)(input, context), ([_4, input]) => If(index_1.Runtime.Token.Const('>', input), ([_5, input]) => [[_0, _1, _2, _3, _4, _5], input])))))), ([_0, input]) => [S.PickMapping(_0, context), input]);
exports.Pick = Pick;
const Omit = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Omit', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const(',', input), ([_3, input]) => If((0, exports.Type)(input, context), ([_4, input]) => If(index_1.Runtime.Token.Const('>', input), ([_5, input]) => [[_0, _1, _2, _3, _4, _5], input])))))), ([_0, input]) => [S.OmitMapping(_0, context), input]);
exports.Omit = Omit;
const Exclude = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Exclude', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const(',', input), ([_3, input]) => If((0, exports.Type)(input, context), ([_4, input]) => If(index_1.Runtime.Token.Const('>', input), ([_5, input]) => [[_0, _1, _2, _3, _4, _5], input])))))), ([_0, input]) => [S.ExcludeMapping(_0, context), input]);
exports.Exclude = Exclude;
const Extract = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Extract', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const(',', input), ([_3, input]) => If((0, exports.Type)(input, context), ([_4, input]) => If(index_1.Runtime.Token.Const('>', input), ([_5, input]) => [[_0, _1, _2, _3, _4, _5], input])))))), ([_0, input]) => [S.ExtractMapping(_0, context), input]);
exports.Extract = Extract;
const Uppercase = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Uppercase', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.UppercaseMapping(_0, context), input]);
exports.Uppercase = Uppercase;
const Lowercase = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Lowercase', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.LowercaseMapping(_0, context), input]);
exports.Lowercase = Lowercase;
const Capitalize = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Capitalize', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.CapitalizeMapping(_0, context), input]);
exports.Capitalize = Capitalize;
const Uncapitalize = (input, context = {}) => If(If(index_1.Runtime.Token.Const('Uncapitalize', input), ([_0, input]) => If(index_1.Runtime.Token.Const('<', input), ([_1, input]) => If((0, exports.Type)(input, context), ([_2, input]) => If(index_1.Runtime.Token.Const('>', input), ([_3, input]) => [[_0, _1, _2, _3], input])))), ([_0, input]) => [S.UncapitalizeMapping(_0, context), input]);
exports.Uncapitalize = Uncapitalize;
const Date = (input, context = {}) => If(index_1.Runtime.Token.Const('Date', input), ([_0, input]) => [S.DateMapping(_0, context), input]);
exports.Date = Date;
const Uint8Array = (input, context = {}) => If(index_1.Runtime.Token.Const('Uint8Array', input), ([_0, input]) => [S.Uint8ArrayMapping(_0, context), input]);
exports.Uint8Array = Uint8Array;
const Reference = (input, context = {}) => If(index_1.Runtime.Token.Ident(input), ([_0, input]) => [S.ReferenceMapping(_0, context), input]);
exports.Reference = Reference;
