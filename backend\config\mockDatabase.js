/**
 * Mock Database for Development/Testing
 * Simulates database operations without requiring a real database
 */

const bcrypt = require('bcryptjs');

// In-memory storage
let mockData = {
  users: [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      password_hash: '$2a$12$LQv3c1yqBwEHxPjjxU/Uy.VM6.tw3ub3R.dDjaTGvJ7BjbBNB.Ciu', // admin123
      role: 'admin',
      first_name: 'System',
      last_name: 'Administrator',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 2,
      username: 'teacher1',
      email: '<EMAIL>',
      password_hash: '$2a$12$LQv3c1yqBwEHxPjjxU/Uy.VM6.tw3ub3R.dDjaTGvJ7BjbBNB.Ciu', // admin123
      role: 'teacher',
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 3,
      username: 'student1',
      email: '<EMAIL>',
      password_hash: '$2a$12$LQv3c1yqBwEHxPjjxU/Uy.VM6.tw3ub3R.dDjaTGvJ7BjbBNB.Ciu', // admin123
      role: 'student',
      first_name: 'Alice',
      last_name: 'Johnson',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ],
  forms: [
    {
      id: 1,
      title: 'Course Feedback Form',
      description: 'Please provide feedback about the course content and delivery',
      created_by: 2,
      status: 'published',
      is_active: true,
      settings: { deadline: '2024-12-31', max_submissions: 1, allow_anonymous: false },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ],
  form_fields: [
    {
      id: 1,
      form_id: 1,
      field_name: 'course_name',
      field_label: 'Course Name',
      field_type: 'text',
      is_required: true,
      field_order: 1,
      options: null,
      validation_rules: { min_length: 2, max_length: 100 },
      placeholder: 'Enter course name',
      help_text: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 2,
      form_id: 1,
      field_name: 'instructor_rating',
      field_label: 'Instructor Rating',
      field_type: 'select',
      is_required: true,
      field_order: 2,
      options: ['Excellent', 'Good', 'Average', 'Poor'],
      validation_rules: null,
      placeholder: null,
      help_text: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ],
  form_submissions: [],
  refresh_tokens: []
};

let nextId = {
  users: 4,
  forms: 2,
  form_fields: 3,
  form_submissions: 1,
  refresh_tokens: 1
};

/**
 * Mock database query execution
 */
async function executeQuery(query, params = []) {
  // Simulate async operation
  await new Promise(resolve => setTimeout(resolve, 10));
  
  const queryLower = query.toLowerCase().trim();
  
  // Handle different query types
  if (queryLower.startsWith('select')) {
    return handleSelect(query, params);
  } else if (queryLower.startsWith('insert')) {
    return handleInsert(query, params);
  } else if (queryLower.startsWith('update')) {
    return handleUpdate(query, params);
  } else if (queryLower.startsWith('delete')) {
    return handleDelete(query, params);
  }
  
  return [];
}

function handleSelect(query, params) {
  const queryLower = query.toLowerCase();
  
  // User queries
  if (queryLower.includes('from users')) {
    if (queryLower.includes('where username =') || queryLower.includes('where email =')) {
      const identifier = params[0];
      const user = mockData.users.find(u => 
        u.username === identifier || u.email === identifier
      );
      return user ? [user] : [];
    } else if (queryLower.includes('where id =')) {
      const id = parseInt(params[0]);
      const user = mockData.users.find(u => u.id === id);
      return user ? [{ ...user, password_hash: undefined }] : [];
    } else if (queryLower.includes('count(*)')) {
      return [{ total: mockData.users.length }];
    } else {
      // Get all users
      return mockData.users.map(u => ({ ...u, password_hash: undefined }));
    }
  }
  
  // Form queries
  if (queryLower.includes('from forms')) {
    if (queryLower.includes('where id =')) {
      const id = parseInt(params[0]);
      const form = mockData.forms.find(f => f.id === id);
      return form ? [form] : [];
    } else if (queryLower.includes('count(*)')) {
      return [{ total: mockData.forms.length }];
    } else {
      return mockData.forms;
    }
  }
  
  // Form fields queries
  if (queryLower.includes('from form_fields')) {
    if (queryLower.includes('where form_id =')) {
      const formId = parseInt(params[0]);
      return mockData.form_fields.filter(f => f.form_id === formId);
    }
    return mockData.form_fields;
  }
  
  // Form submissions queries
  if (queryLower.includes('from form_submissions')) {
    if (queryLower.includes('where submitted_by =')) {
      const userId = parseInt(params[0]);
      return mockData.form_submissions.filter(s => s.submitted_by === userId);
    } else if (queryLower.includes('count(*)')) {
      return [{ 
        total_submissions: mockData.form_submissions.length,
        submitted_count: mockData.form_submissions.filter(s => s.status === 'submitted').length,
        draft_count: mockData.form_submissions.filter(s => s.status === 'draft').length,
        reviewed_count: mockData.form_submissions.filter(s => s.status === 'reviewed').length,
        average_score: 0
      }];
    }
    return mockData.form_submissions;
  }
  
  return [];
}

function handleInsert(query, params) {
  const queryLower = query.toLowerCase();
  
  if (queryLower.includes('into users')) {
    const newUser = {
      id: nextId.users++,
      username: params[0],
      email: params[1],
      password_hash: params[2],
      role: params[3],
      first_name: params[4],
      last_name: params[5],
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    mockData.users.push(newUser);
    return { insertId: newUser.id };
  }
  
  if (queryLower.includes('into forms')) {
    const newForm = {
      id: nextId.forms++,
      title: params[0],
      description: params[1],
      created_by: params[2],
      status: params[3],
      settings: JSON.parse(params[4] || '{}'),
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    mockData.forms.push(newForm);
    return { insertId: newForm.id };
  }
  
  if (queryLower.includes('into form_submissions')) {
    const newSubmission = {
      id: nextId.form_submissions++,
      form_id: params[0],
      submitted_by: params[1],
      submission_data: JSON.parse(params[2]),
      status: params[3],
      score: params[4],
      feedback: params[5],
      submitted_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    mockData.form_submissions.push(newSubmission);
    return { insertId: newSubmission.id };
  }
  
  if (queryLower.includes('into refresh_tokens')) {
    const newToken = {
      id: nextId.refresh_tokens++,
      user_id: params[0],
      token: params[1],
      expires_at: params[2],
      created_at: new Date().toISOString()
    };
    mockData.refresh_tokens.push(newToken);
    return { insertId: newToken.id };
  }
  
  return { insertId: 1 };
}

function handleUpdate(query, params) {
  // Simple update simulation
  return { affectedRows: 1 };
}

function handleDelete(query, params) {
  // Simple delete simulation
  return { affectedRows: 1 };
}

/**
 * Test connection (always succeeds for mock)
 */
async function testConnection() {
  console.log('✅ Mock database connected successfully');
  return true;
}

/**
 * Get connection (returns mock connection)
 */
async function getConnection() {
  return {
    execute: executeQuery,
    beginTransaction: async () => {},
    commit: async () => {},
    rollback: async () => {},
    release: () => {}
  };
}

module.exports = {
  executeQuery,
  testConnection,
  getConnection,
  pool: { getConnection }
};
