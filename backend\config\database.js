/**
 * Database configuration for PlanetScale MySQL
 * Supports both connection string and individual parameters
 * Uses mock database in development when no real database is available
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// Use mock database in development if no real database is configured
if (process.env.NODE_ENV === 'development' && process.env.DB_HOST === 'localhost') {
  console.log('🔧 Using mock database for development');
  module.exports = require('./mockDatabase');
  return;
}

/**
 * Database connection configuration
 * PlanetScale requires SSL and specific connection settings
 */
const dbConfig = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: process.env.DB_PORT || 3306,
  ssl: {
    rejectUnauthorized: true
  },
  // Connection pool settings
  connectionLimit: 10,
  queueLimit: 0,
  // Charset for proper Unicode support
  charset: 'utf8mb4'
};

/**
 * Create connection pool for better performance
 * Pool automatically handles connection management
 */
const pool = mysql.createPool(dbConfig);

/**
 * Test database connection
 * @returns {Promise<boolean>} Connection status
 */
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ Database connected successfully to PlanetScale');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

/**
 * Execute a query with error handling
 * @param {string} query - SQL query
 * @param {Array} params - Query parameters
 * @returns {Promise<Array>} Query results
 */
async function executeQuery(query, params = []) {
  try {
    const [results] = await pool.execute(query, params);
    return results;
  } catch (error) {
    console.error('Database query error:', error.message);
    throw error;
  }
}

/**
 * Get a connection from the pool
 * Use this for transactions
 * @returns {Promise<Connection>} Database connection
 */
async function getConnection() {
  return await pool.getConnection();
}

module.exports = {
  pool,
  testConnection,
  executeQuery,
  getConnection
};
