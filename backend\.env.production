# SkillUp Lab Production Environment Configuration
# Copy this file to .env and update with your production values
# DO NOT commit this file to version control with real values

# =============================================================================
# REQUIRED CONFIGURATION - Must be set for production
# =============================================================================

# Server Configuration
PORT=3000
NODE_ENV=production

# Database Configuration (PlanetScale) - REQUIRED
# Get your connection string from: https://app.planetscale.com/
# Format: mysql://username:<EMAIL>/database_name?sslaccept=strict
DATABASE_URL=REPLACE_WITH_YOUR_PLANETSCALE_CONNECTION_STRING

# JWT Configuration - REQUIRED (Generate secure random strings)
# Use: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
# These MUST be different from each other and at least 64 characters long
JWT_SECRET=REPLACE_WITH_64_CHARACTER_RANDOM_STRING_FOR_PRODUCTION_JWT_TOKENS
JWT_REFRESH_SECRET=REPLACE_WITH_DIFFERENT_64_CHARACTER_RANDOM_STRING_FOR_REFRESH_TOKENS
JWT_EXPIRE=1h
JWT_REFRESH_EXPIRE=7d

# CORS Configuration (Production Domains Only)
# Replace with your actual production domain(s) - NO localhost URLs
CORS_ORIGIN=https://yourdomain.com,https://www.yourdomain.com

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Password Hashing
BCRYPT_SALT_ROUNDS=12

# Rate Limiting (Production Values)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=REPLACE_WITH_64_CHARACTER_RANDOM_STRING_FOR_SESSIONS
SESSION_TIMEOUT=3600

# Security Headers
ENABLE_SECURITY_HEADERS=true
CONTENT_SECURITY_POLICY=default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Logging Configuration
LOG_LEVEL=warn
ENABLE_QUERY_LOGGING=false
SLOW_QUERY_THRESHOLD=1000

# Email Configuration (Optional - for notifications and user verification)
SMTP_HOST=smtp.yourmailprovider.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=your-smtp-username
SMTP_PASS=your-smtp-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=SkillUp Lab

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,txt

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_SAMPLE_RATE=0.1

# Error Reporting (Optional - Sentry integration)
SENTRY_DSN=your-sentry-dsn-for-error-tracking
SENTRY_ENVIRONMENT=production

# Analytics (Optional)
ENABLE_ANALYTICS=false
ANALYTICS_API_KEY=your-analytics-api-key

# Backup Configuration (if using automated backups)
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Core Features
FEATURE_REGISTRATION=true
FEATURE_EMAIL_VERIFICATION=false
FEATURE_PASSWORD_RESET=true

# Form Features
FEATURE_FORM_TEMPLATES=true
FEATURE_FORM_ANALYTICS=true
FEATURE_BULK_OPERATIONS=true
FEATURE_FORM_EXPORT=true

# API Features
FEATURE_API_ACCESS=true
FEATURE_WEBHOOKS=false

# Admin Features
FEATURE_AUDIT_LOGS=true
FEATURE_SYSTEM_MONITORING=true
FEATURE_USER_MANAGEMENT=true

# =============================================================================
# THIRD-PARTY INTEGRATIONS (Optional)
# =============================================================================

# Cloud Storage (Optional - for file uploads)
CLOUD_STORAGE_PROVIDER=local
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-s3-bucket
AWS_S3_REGION=us-east-1

# Redis (Optional - for session storage and caching)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# Webhook Configuration (Optional)
WEBHOOK_SECRET=your-webhook-secret-for-verification

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Application Metadata
APP_NAME=SkillUp Lab
APP_VERSION=1.0.0
APP_DESCRIPTION=Professional form management and submission platform

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health

# Graceful Shutdown
GRACEFUL_SHUTDOWN_TIMEOUT=30000

# =============================================================================
# INSTRUCTIONS FOR SETUP
# =============================================================================

# 1. Copy this file to .env in the backend directory
# 2. Replace ALL "REPLACE_WITH_" values with actual production values
# 3. Generate secure random strings using:
#    node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
# 4. Set up your PlanetScale database and get the connection string
# 5. Configure your production domain in CORS_ORIGIN
# 6. Set up SMTP if you want email functionality
# 7. Configure monitoring and error reporting services
# 8. Test the configuration in a staging environment first
# 9. Never commit the .env file with real values to version control

# =============================================================================
# SECURITY CHECKLIST
# =============================================================================

# ✅ All secrets are randomly generated and at least 64 characters
# ✅ DATABASE_URL points to production PlanetScale database
# ✅ CORS_ORIGIN only includes production domains (no localhost)
# ✅ NODE_ENV is set to "production"
# ✅ LOG_LEVEL is set to "warn" or "error" for production
# ✅ All default passwords have been changed
# ✅ SMTP credentials are configured if email features are enabled
# ✅ Error reporting is configured for production monitoring
# ✅ File upload restrictions are properly configured
# ✅ Rate limiting is enabled and properly configured
