<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Skill Up – Welcome</title>
  <link rel="stylesheet" href="style.css" />
  <link rel="icon" type="image/png" href="images/ChatGPT Image May 5, 2025, 12_54_09 AM.png">
<!-- Google Font Link -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@600;700&display=swap" rel="stylesheet">
</head>

<body>

  <!-- NAVBAR -->
   <div class="head">
  <header class="navbar slide-down">
    <div class="container">
      <div class="logo">
        <img src="images/bglogo.png" alt="Eco Spark Logo" class="logo">

      </div>

      <nav class="nav-links" id="navLinks">
        <a href="index.html">Home</a>
        <a href="learner-page.html">For Learner</a>
        <a href="teacher-page.html">For Teachers</a>
        <a href="Guide.html">Guide</a>
        <a href="contact.html">Contact</a>

        <!-- Authentication Links -->
        <div class="auth-nav">
          <span class="user-info" style="display: none;"></span>
          <a href="login.html" class="guest-only">Login</a>
          <a href="register.html" class="guest-only">Register</a>
          <a href="#" class="auth-required logout-btn" style="display: none;">Logout</a>
        </div>
      </nav>

      <button class="menu-icon" id="menuToggle" aria-label="Toggle menu">
        <span></span><span></span><span></span>
      </button>
    </div>
  </header>

  <!-- MOBILE MENU -->
  <nav class="mobile-menu" id="mobileMenu">
    <a href="index.html">Home</a>
    <a href="learner-page.html">For Students</a>
    <a href="teacher-page.html">For Teachers</a>
    <a href="Guide.html">Guide</a>
    <a href="contact.html">Contact</a>
  </nav>

  <!-- HERO -->
  <section class="hero">
    <div class="hero-inner fade-up delay-1">
    <h1>Unlock Your Potential With <br>
       SkillUp Today</h1>
  <p>
   Empowering everyday people to earn by teaching what they know — while helping others grow from anywhere in the world.
  </p>
      <div class="cta-group delay-3">
        <a href="teacher-page.html" class="hero-cta-btn-1">Teach</a>
        <a href="learner-page.html" class="hero-cta-btn-2">Learn</a>
      </div>

    </div>
  </section>



 <!-- HOW IT WORKS -->
<section class="how-it-works reveal">
  <h2>How SkillUp Works</h2>

  <div class="steps">

    <div class="step">
      <img src="images/download1r.jpg" alt="register form" class="picard">
      <h3 class="card-btn">1. Create Your Profile</h3>
      <p>Whether you're here to learn or teach, it starts with a simple form. Share your goals, location, budget, and preferred learning style. We use this to tailor your experience.</p>
    </div>

    <div class="step">
      <img src="images/download2r.jpg" alt="matchmaking" class="picard">
      <h3 class="card-btn">2. Get Matched & Start Classes</h3>
      <p>Learners are matched with verified teachers based on subject, availability, and learning vibe. Classes happen on WhatsApp, Zoom, or in-person — flexible and focused on results.</p>
    </div>

    <div class="step">
      <img src="images/download4.jpg" alt="certificates and success" class="picard">
      <h3 class="card-btn">3. Grow, Earn & Get Certified</h3>
      <p>After each class or program, learners receive digital certificates. Teachers grow their visibility with reviews and results. Share your success, inspire others, and build your brand on Skill Up.</p>
    </div>
  </div>

       <div class="cta-grou delay-3">
      <a href="teachers account.html" class="how-cta-btn-1">Build your brand</a>
      <a href="learners-account.html" class="how-cta-btn-2">UnLock Potentials</a>
    </div>
</section>


  <!-- BENEFITS -->
<section class="benefits reveal">
  <h2>Why Choose Skill Up</h2>
  <div class="steps">
    <div class="step benefits">
      <img src="images/downloads1.jpg" alt="filling a form" class="picard">
      <h3>1. Real Human Connections</h3>
      <p>Providing you with the best Learning Enviroment from online to hybrid in Real Time. <br>Learn with others, ask questions, and grow in real time.</p>
    </div>
    <div class="step benefits">
      <img src="images/downloads2.jpg" alt="get matched" class="picard">
      <h3>2. Teach & Earn</h3>
      <p>Anyone with a skill can become a tutor and earn, even without a formal job or certificate.</p>
    </div>
    <div class="step benefits">
      <img src="images/downloads4.jpg" alt="share success" class="picard">
      <h3>4. Education as Empowerment</h3>
      <p>Learn Anything From Real People Who Speak your language, <br> Build Your MiniSchool Remotely. Grow your community.</p>
    </div>
  </div>

 <div class="cta-grou delay-3">
      <a href="teacher-page.html" class="how-cta-btn-1">Teach and Earn</a>
      <a href="learner-page.html" class="how-cta-btn-2"> Learn and Grow</a>
    </div>
  </section>

<!-- TESTIMONIALS / CONTACT FORM -->
<!-- CONTACT FORM -->
<section class="section contact-form-section" id="con">
  <form class="form-container" id="skillUpForm">
    <h2 style="text-align: center;">Join the Skill Up Movement</h2>
    <p class="form-intro" style="text-align: center;font-size: 1.1rem;">
  Give us any idea, feedback, or issue — Let's build Skill Up together.
</p>

    <div class="input-icon">
      <i class="fas fa-user"></i>
      <input type="text" name="name" placeholder="Full Name" required>
    </div>

    <div class="input-icon">
      <i class="fas fa-envelope"></i>
      <input type="email" name="email" placeholder="Email Address" >
    </div>

    <div class="phone-field">
<select name="countryCode" required>
  <option value="">Select Code</option>
  <option value="+93">Afghanistan (+93)</option>
  <option value="+355">Albania (+355)</option>
  <option value="+213">Algeria (+213)</option>
  <option value="+376">Andorra (+376)</option>
  <option value="+244">Angola (+244)</option>
  <option value="+54">Argentina (+54)</option>
  <option value="+374">Armenia (+374)</option>
  <option value="+61">Australia (+61)</option>
  <option value="+43">Austria (+43)</option>
  <option value="+994">Azerbaijan (+994)</option>
  <option value="+973">Bahrain (+973)</option>
  <option value="+880">Bangladesh (+880)</option>
  <option value="+375">Belarus (+375)</option>
  <option value="+32">Belgium (+32)</option>
  <option value="+229">Benin (+229)</option>
  <option value="+591">Bolivia (+591)</option>
  <option value="+387">Bosnia & Herzegovina (+387)</option>
  <option value="+267">Botswana (+267)</option>
  <option value="+55">Brazil (+55)</option>
  <option value="+359">Bulgaria (+359)</option>
  <option value="+226">Burkina Faso (+226)</option>
  <option value="+257">Burundi (+257)</option>
  <option value="+855">Cambodia (+855)</option>
  <option value="+237">Cameroon (+237)</option>
  <option value="+1">Canada (+1)</option>
  <option value="+238">Cape Verde (+238)</option>
  <option value="+236">Central African Republic (+236)</option>
  <option value="+235">Chad (+235)</option>
  <option value="+56">Chile (+56)</option>
  <option value="+86">China (+86)</option>
  <option value="+57">Colombia (+57)</option>
  <option value="+269">Comoros (+269)</option>
  <option value="+506">Costa Rica (+506)</option>
  <option value="+385">Croatia (+385)</option>
  <option value="+53">Cuba (+53)</option>
  <option value="+357">Cyprus (+357)</option>
  <option value="+420">Czech Republic (+420)</option>
  <option value="+45">Denmark (+45)</option>
  <option value="+253">Djibouti (+253)</option>
  <option value="+593">Ecuador (+593)</option>
  <option value="+20">Egypt (+20)</option>
  <option value="+503">El Salvador (+503)</option>
  <option value="+240">Equatorial Guinea (+240)</option>
  <option value="+291">Eritrea (+291)</option>
  <option value="+372">Estonia (+372)</option>
  <option value="+268">Eswatini (+268)</option>
  <option value="+251">Ethiopia (+251)</option>
  <option value="+679">Fiji (+679)</option>
  <option value="+358">Finland (+358)</option>
  <option value="+33">France (+33)</option>
  <option value="+241">Gabon (+241)</option>
  <option value="+220">Gambia (+220)</option>
  <option value="+995">Georgia (+995)</option>
  <option value="+49">Germany (+49)</option>
  <option value="+233">Ghana (+233)</option>
  <option value="+30">Greece (+30)</option>
  <option value="+502">Guatemala (+502)</option>
  <option value="+224">Guinea (+224)</option>
  <option value="+245">Guinea-Bissau (+245)</option>
  <option value="+592">Guyana (+592)</option>
  <option value="+509">Haiti (+509)</option>
  <option value="+504">Honduras (+504)</option>
  <option value="+852">Hong Kong (+852)</option>
  <option value="+36">Hungary (+36)</option>
  <option value="+91">India (+91)</option>
  <option value="+62">Indonesia (+62)</option>
  <option value="+98">Iran (+98)</option>
  <option value="+964">Iraq (+964)</option>
  <option value="+353">Ireland (+353)</option>
  <option value="+972">Israel (+972)</option>
  <option value="+39">Italy (+39)</option>
  <option value="+225">Ivory Coast (+225)</option>
  <option value="+81">Japan (+81)</option>
  <option value="+254">Kenya (+254)</option>
  <option value="+965">Kuwait (+965)</option>
  <option value="+961">Lebanon (+961)</option>
  <option value="+266">Lesotho (+266)</option>
  <option value="+218">Libya (+218)</option>
  <option value="+370">Lithuania (+370)</option>
  <option value="+352">Luxembourg (+352)</option>
  <option value="+261">Madagascar (+261)</option>
  <option value="+265">Malawi (+265)</option>
  <option value="+60">Malaysia (+60)</option>
  <option value="+223">Mali (+223)</option>
  <option value="+356">Malta (+356)</option>
  <option value="+222">Mauritania (+222)</option>
  <option value="+230">Mauritius (+230)</option>
  <option value="+52">Mexico (+52)</option>
  <option value="+212">Morocco (+212)</option>
  <option value="+258">Mozambique (+258)</option>
  <option value="+95">Myanmar (+95)</option>
  <option value="+264">Namibia (+264)</option>
  <option value="+977">Nepal (+977)</option>
  <option value="+31">Netherlands (+31)</option>
  <option value="+64">New Zealand (+64)</option>
  <option value="+234">Nigeria (+234)</option>
  <option value="+47">Norway (+47)</option>
  <option value="+92">Pakistan (+92)</option>
  <option value="+507">Panama (+507)</option>
  <option value="+595">Paraguay (+595)</option>
  <option value="+51">Peru (+51)</option>
  <option value="+63">Philippines (+63)</option>
  <option value="+48">Poland (+48)</option>
  <option value="+351">Portugal (+351)</option>
  <option value="+974">Qatar (+974)</option>
  <option value="+40">Romania (+40)</option>
  <option value="+7">Russia (+7)</option>
  <option value="+250">Rwanda (+250)</option>
  <option value="+966">Saudi Arabia (+966)</option>
  <option value="+221">Senegal (+221)</option>
  <option value="+65">Singapore (+65)</option>
  <option value="+27">South Africa (+27)</option>
  <option value="+82">South Korea (+82)</option>
  <option value="+34">Spain (+34)</option>
  <option value="+94">Sri Lanka (+94)</option>
  <option value="+249">Sudan (+249)</option>
  <option value="+46">Sweden (+46)</option>
  <option value="+41">Switzerland (+41)</option>
  <option value="+886">Taiwan (+886)</option>
  <option value="+255">Tanzania (+255)</option>
  <option value="+66">Thailand (+66)</option>
  <option value="+228">Togo (+228)</option>
  <option value="+216">Tunisia (+216)</option>
  <option value="+90">Turkey (+90)</option>
  <option value="+256">Uganda (+256)</option>
  <option value="+380">Ukraine (+380)</option>
  <option value="+971">UAE (+971)</option>
  <option value="+44">UK (+44)</option>
  <option value="+1">USA (+1)</option>
  <option value="+58">Venezuela (+58)</option>
  <option value="+84">Vietnam (+84)</option>
  <option value="+967">Yemen (+967)</option>
  <option value="+260">Zambia (+260)</option>
  <option value="+263">Zimbabwe (+263)</option>
</select>

      <div class="input-icon">
        <i class="fab fa-whatsapp"></i>
        <input type="tel" name="whatsapp" placeholder="WhatsApp Number (fastest)" required pattern="[0-9]{7,15}">
      </div>
    </div>

    <div class="input-icon">
      <i class="fas fa-comment-dots"></i>
      <textarea name="message" rows="4" placeholder="Tell us more..." required></textarea>
    </div>

    <button type="submit">Send Message</button>
  </form>
</section>


   </div>



 <!-- FOOTER -->
<footer class="footer slide-up">
  <div class="footer-links">
    <a href="index.html">Home</a>
    <a href="about.html">About</a>
    <a href="addskill.html">Add a Skill</a>
    <a href="learn.html">Learn a Skill</a>
    <a href="contact.html">Contact</a>
  </div>

  <p style="font-size: 1rem;">
  <strong>Skill Up MVP</strong>   </p>
  <p>
    Got ideas or something’s not working? <a href="contact.html" style="color: #111;">Reach out.</a> 
    We’re listening.
  </p>

  <p>&copy; 2025 Skill Up. All rights reserved.</p>
</footer>


  <script src="js/api.js"></script>
  <script src="js/auth.js"></script>
  <script>
    const menuToggle = document.getElementById('menuToggle');
    const mobileMenu = document.getElementById('mobileMenu');
    menuToggle.addEventListener('click', () => {
      mobileMenu.classList.toggle('open');
      menuToggle.classList.toggle('open');
    });

    // Close mobile menu when any link is clicked
    const mobileLinks = mobileMenu.querySelectorAll('a');
    mobileLinks.forEach(link => {
      link.addEventListener('click', () => {
        mobileMenu.classList.remove('open');
        menuToggle.classList.remove('open');
      });
    });

    // Scroll reveal
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('in-view');
        }
      });
    }, { threshold: 0.15 });
    document.querySelectorAll('.reveal').forEach(el => observer.observe(el));

  document.getElementById('skillUpForm').addEventListener('submit', function (e) {
    const form = e.target;
    const phone = form.whatsapp.value.trim();
    if (!/^\d{7,15}$/.test(phone)) {
      alert("Please enter a valid WhatsApp number.");
      e.preventDefault();
    }
  });

  </script>

</body>

</html>