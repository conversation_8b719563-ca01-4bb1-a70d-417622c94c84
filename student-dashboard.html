<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Dashboard - SkillUp Lab</title>
    <link rel="stylesheet" href="style.css">
    <link rel="icon" type="image/png" href="images/ChatGPT Image May 5, 2025, 12_54_09 AM.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        .dashboard-header {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .dashboard-header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5rem;
        }

        .dashboard-header p {
            margin: 0;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #74b9ff;
        }

        .dashboard-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .dashboard-card h3 i {
            color: #74b9ff;
        }

        .forms-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .form-item {
            padding: 15px;
            border: 1px solid #e1e1e1;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .form-item:hover {
            border-color: #74b9ff;
            transform: translateY(-2px);
        }

        .form-item h4 {
            margin: 0 0 5px 0;
            color: #333;
        }

        .form-item p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }

        .form-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-top: 5px;
        }

        .status-published {
            background: #d4edda;
            color: #155724;
        }

        .status-submitted {
            background: #cce5ff;
            color: #004085;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #74b9ff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #ccc;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #74b9ff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #0984e3;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #74b9ff;
            color: #74b9ff;
        }

        .btn-outline:hover {
            background: #74b9ff;
            color: white;
        }

        .loading {
            text-align: center;
            padding: 40px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #74b9ff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .close {
            position: absolute;
            right: 20px;
            top: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #74b9ff;
        }

        .form-group small {
            display: block;
            margin-top: 5px;
            color: #666;
            font-size: 14px;
        }

        .radio-label,
        .checkbox-label {
            display: block;
            margin: 8px 0;
            cursor: pointer;
        }

        .radio-label input,
        .checkbox-label input {
            width: auto;
            margin-right: 8px;
        }

        .submission-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .submission-data {
            margin: 20px 0;
        }

        .response-item {
            padding: 10px;
            border-bottom: 1px solid #e1e1e1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .response-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <img src="images/bglogo.png" alt="SkillUp Logo">
                <span>SkillUp Lab</span>
            </div>
            <nav class="nav-links">
                <a href="index.html">Home</a>
                <span class="user-info"></span>
                <a href="#" class="logout-btn">Logout</a>
            </nav>
        </div>
    </header>

    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1><i class="fas fa-graduation-cap"></i> Student Dashboard</h1>
            <p>Welcome to your learning portal</p>
        </div>

        <div class="dashboard-grid">
            <!-- Available Forms -->
            <div class="dashboard-card">
                <h3><i class="fas fa-clipboard-list"></i> Available Forms</h3>
                <div id="availableForms" class="forms-list">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>Loading forms...</p>
                    </div>
                </div>
            </div>

            <!-- My Submissions -->
            <div class="dashboard-card">
                <h3><i class="fas fa-paper-plane"></i> My Submissions</h3>
                <div id="mySubmissions" class="forms-list">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>Loading submissions...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="dashboard-card">
            <h3><i class="fas fa-chart-bar"></i> My Statistics</h3>
            <div id="studentStats" class="stats-grid">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Loading statistics...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Modal -->
    <div id="formModal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="formContent"></div>
        </div>
    </div>

    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/student-dashboard.js"></script>
</body>
</html>
