<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Skill Up – Create Teachers Account</title>
    <link href="https://fonts.googleapis.com/css?family=Raleway" rel="stylesheet">
    <link rel="stylesheet" href="anim.css">
    <link rel="icon" type="image/png" href="images/ChatGPT Image May 5, 2025, 12_54_09 AM.png">
    <script src="https://cdn.jsdelivr.net/npm/@yaireo/tagify"></script>
    <style>
        /* Footer */
        .footer {
            background: #a3fff4;
            color: black;
            text-align: center;
            padding: 2rem 1rem;
        }

        .footer-links {
            margin-bottom: 1rem;
        }

        .footer-links a {
            color: black;
            margin: 0 1rem;
            text-decoration: none;
        }

        @media screen and (max-width: 600px) {
            .footer-links a {
                display: inline-block;
            }
        }

        .head {
            text-align: center;
            margin-bottom: 25px;
            background-color: #a3fff4;
            width: auto;
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 0rem;
        }

        .logo {
            color: white;
            font-size: 1.6rem;
            font-weight: bold;
            height: 6vh;
        }

        span {
            color: black;
            font-size: 20px;
            font-weight: bolder;
            word-spacing: 10px;
            padding-bottom: 10px;
        }
        body{
          background-color: white;
          color: orange;
        }
        .hero{
          background-color: #a3fff4;
          height: 80vh;
        }
        .hero h1{
          font-size: 2.8rem;
          text-align: center;
          color: orange;
          margin-top:7%;
        }
        .hero p{
          text-align: center;
          font-size: 1.3rem;
        }

.well p, h1 {
  text-align: center;
}
.well h1{
  font-size: 3rem;
  color: orange;
  border-bottom: 3px solid black;
  width: 50vw;
  margin: 10%;
  margin-left: 25%;
}


    </style>
</head>

<body>
    <header class="head">
        <div class="logos">
            <img src="images/bglogo.png" alt="Eco Spark Logo" class="logo">

        </div>
        <span>
            <a href="learner.html">Learn</a>
            <a href="teacher.html">Teach</a>
            <a href="index.html">Grow</a>
        </span>
    </header>


    <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Skill Up | User Guide</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
</head>
<body style="font-family: 'Segoe UI', sans-serif; margin: 0; background-color: #f8f8f8; color: #333;">

  <!-- HEADER -->
  <header style="background-color: #006644; padding: 20px; color: white; text-align: center;">
    <h1 style="margin: 0;">Skill Up - User Guide</h1>
    <p style="margin-top: 5px;">Everything you need to know as a Student or Teacher</p>
  </header>

  <!-- MAIN GUIDE SECTION -->
  <section style="padding: 30px 15px; max-width: 1000px; margin: auto;">
    <!-- STUDENT GUIDE -->
    <div style="margin-bottom: 50px;">
      <h2 style="color: #006644;">For Students</h2>
      <p><strong>1. Creating Your Learner Profile:</strong> Head to the <a href="learner-acc.html" style="color:#006644;">Learner Registration Page</a>. Fill in your interests, preferred learning method (in-person or online), and your available schedule.</p>

      <p><strong>2. Finding a Teacher:</strong> Once your profile is complete, our team will match you with verified teachers based on your subject, location, and preferred mode of learning.</p>

      <p><strong>3. Booking Classes:</strong> Book sessions directly via WhatsApp with your matched teacher. We suggest setting your goals and number of sessions from the start for clarity.</p>

      <p><strong>4. Payment Process:</strong> You pay your teacher directly — Skill Up does not take any cut at this stage. Always request a receipt after payment.</p>

      <p><strong>5. Support & Disputes:</strong> Reach out to the Skill Up support team via WhatsApp if you experience issues with your teacher or learning sessions.</p>

      <p><strong>6. Learning Tips:</strong> Stay consistent, ask questions, and let your teacher know what works best for you. Your feedback helps us improve!</p>
    </div>

    <!-- TEACHER GUIDE -->
    <div>
      <h2 style="color: #006644;">For Teachers</h2>
      <p><strong>1. Becoming a Skill Up Teacher:</strong> Register on the <a href="teach.html" style="color:#006644;">Teacher Registration Page</a>. You'll be asked to list your skills, subjects, rates, availability, and preferred teaching mode.</p>

      <p><strong>2. Profile Screening:</strong> Our team will verify your submission and contact you to complete onboarding. This may include a quick call or identity confirmation.</p>

      <p><strong>3. Getting Matched:</strong> Once approved, we’ll begin matching you with students who fit your profile. You’ll be contacted via WhatsApp for each match.</p>

      <p><strong>4. Teaching Your Way:</strong> You’re free to structure your sessions as you wish. Be clear with students on your availability, fees, and preferred materials.</p>

      <p><strong>5. Receiving Payments:</strong> Students pay you directly. You are responsible for confirming payment and delivering the agreed sessions.</p>

      <p><strong>6. Community Conduct:</strong> Respect your learners, maintain professionalism, and communicate clearly. Teachers who get multiple negative reviews may be removed.</p>
    </div>
  </section>

  <!-- CONTACT AND SOCIALS -->
  <section style="background-color: #e9f5ec; padding: 25px 15px; text-align: center;">
    <p><strong>Need Help?</strong> Message us directly via WhatsApp <br> 
    <a href="https://wa.me/234XXXXXXXXXX" style="color: #006644; font-weight: bold;">+234-XXXXXXXXXX</a></p>

    <p style="margin-top: 15px;">
      <a href="#" style="margin: 0 10px; color: #006644;"><i class="fab fa-facebook-f"></i></a>
      <a href="#" style="margin: 0 10px; color: #006644;"><i class="fab fa-twitter"></i></a>
      <a href="#" style="margin: 0 10px; color: #006644;"><i class="fab fa-instagram"></i></a>
    </p>
  </section>



    <footer class="footer">
        <div class="footer-links">
            <a href="index.html">Home</a>
            <a href="about.html">About</a>
            <a href="skills.html">Add a Skill</a>
            <a href="learn.html">Learn a Skill</a>
            <a href="contact.html">Contact</a>
        </div>
 <p style="font-size: 1rem;">
            You’re viewing the <strong>Skill Up MVP (Minimum Viable Product)</strong>. We’re testing, improving, and
            building the full platform — and your feedback will shape the future of Skill Up.
        </p>
        <p>
       Got ideas or issues?<a href="contact.html" style="color: #111;">Tell us.</a> Your voice matters.
        </p>
  <p>&copy; 2025 Skill Up. All rights reserved.</p>
    </footer>


</body>

</html>