@charset "UTF-8";
@import url(https://fonts.googleapis.com/css2?family=Montserrat&family=Oswald&display=swap);


/* Reset & base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", sans-serif;
  line-height: 1.6;
  background: #f9f9f9;
  color: #222;
}

/* Navbar */
.navbar {
  background: linear-gradient(to left, blue,white);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  animation: slideDownAnim 0.6s ease forwards;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  color: white;
  font-size: 1.6rem;
  font-weight: bold;
}

.nav-links {
  display: flex;
  gap: 1.5rem;
}

.nav-links a {
  color: white;
  font-size: 18px;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}



/* Hamburger */
.menu-icon {
  background: none;
  border: none;
  display: none;
  cursor: pointer;
  flex-direction: column;
  gap: 5px;
}

.menu-icon span {
  width: 25px;
  height: 3px;
  background-color: white;
  transition: all 0.3s ease;
}

.menu-icon.open span:nth-child(1) {
  transform: rotate(45deg) translateY(8px);
}

.menu-icon.open span:nth-child(2) {
  opacity: 0;
}

.menu-icon.open span:nth-child(3) {
  transform: rotate(-45deg) translateY(-8px);
}

.mobile-menu {
  position: fixed;
  width: 200px;
  height: auto;
  top: 90px;
  /* below navbar */
  left: 30;
  right: 0;
  background:linear-gradient(to bottom, blue, navy);
  flex-direction: column;
  text-align: center;
  gap: 1rem;
  padding: 0;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  z-index: 999;
  transition: max-height 0.4s ease, opacity 0.4s ease;
}

.mobile-menu a {
  color: white;
  text-decoration: none;
  font-size: 1.1rem;
  padding: 1rem;
  display: block;
}

.mobile-menu.open {
  max-height: 300px;
  opacity: 1;
  padding-bottom: 2rem 0 6rem;
}

img.logo {
  height: 50px;
  /* Adjust the height as needed */
  margin-right: 15px;
}

/* Hero Section */
.hero {
  padding-top: 1.6rem;
  text-align: center;
  background:url(images/forestbridge.jpg);
  background-repeat: no-repeat;
  background-size: cover;
filter: brightness(-40);
  height: 100vh;
  color: white;
}

.hero-inner {
  max-width: 800px;
  margin: 0 auto;
}

.hero h1 {
  font-size: 3rem;
  font-weight: bolder;
  font-family: papyru;
  padding-top: 9%;
}

.hero p {
  font-size: 1.2rem;
  font-weight: 480;
  margin-bottom: 2rem;
  color:whitesmoke;
  font-family: Cambria;
}
@media screen and (max-width: 600px){
  .hero h1{
font-size: 2.5rem;
  }
  .hero p{
    font-size: 1.2rem;
    font-weight: 350;
  }
}
.hero-cta-btn-1{
  border: 3px solid navy;
  text-decoration: none;
  font-size: 1.3rem;
  font-weight: bold;
  padding: 12px 35px ;
color: white;
border-radius: 20px;
background:black;
margin-right: 20px;
font-family: Consolas;
}
.hero-cta-btn-2{
  border: 3px solid green;
  text-decoration: none;
  font-size: 1.3rem;
  font-weight: bold;
  padding: 12px 35px ;
color: white;
border-radius: 20px;
background:black;
font-family: Consolas;
}
.hero-cta-btn-1:hover{
background:white;
color: black;
transition: 1s;
  transform: translateY(-28px);

}
.hero-cta-btn-2:hover{
  background: white;
color: black;
transition: 1s;
  transform: translateY(-28px);
}


/* Sections */
section {
  padding: 4rem 2rem;
  background: transparent;
}

/* how it works section */
.how-it-works{
  margin: 0 auto; 
  background-color: #F0FFF5;
}
.how-it-works h2{
  font-size: 3rem;
  font-family: Raleway;
  text-align: center;
   color: navy;
}
.steps h3 {
  font-size: 20px;
  margin-bottom: 10px;
  color: darkorange;
}
.steps {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  margin: 40px 0;
}
.step {
  background: transparent;
  border-radius: 10px;
  width: 400px;
  padding: 20px;
  text-align: center;
  transition: transform 0.3s ease;
}

.step:hover {
  transform: translateY(-28px);
}
.step p {
  font-size: 16px;
  line-height: 1.5;
  color: black;
}
.how-cta-btn-1{
  border: 3px solid navy;
  text-decoration: none;
  font-size: 1.3rem;
  font-weight: bold;
  padding: 12px 35px ;
color: white;
border-radius: 20px;
background:black;
margin-right: 20px;
font-family: Consolas;
}
.how-cta-btn-2{
  border: 3px solid green;
  text-decoration: none;
  font-size: 1.3rem;
  font-weight: bold;
  padding: 12px 35px ;
color: white;
border-radius: 20px;
background:black;
font-family: Consolas;
}
.cta-grou {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap; /* Allows them to wrap on small screens */
  gap: 20px; /* Spacing between buttons */
  margin-top: 3%;
}

/* benefits */
/* */
.benefits{
  background-color: indigo;
}
.benefits h2{
  text-align: center;
  font-size: 2.6rem;
  color: white;
}
@media screen and (max-width: 600px){
  .benefits h2{
    font-size: 2rem;
  }
}
.benefits h3{
  color: white;
}
.benefits p{
  color: white;
}
@media (max-width: 768px) {
  .steps {
    flex-direction: column;
    align-items: center;
  }

  .step {
    width: 90%;
  }
  .how-it-works h2{
  font-size: 2.2rem;
}
}
/* contact */

.contact-form-section {
  background-color:  #F0FFF5;
  padding: 60px 20px;
  text-align: center;
}

.form-container {
  max-width: 420px;
  margin: 0 auto;
  background: #fff8f2;
  border: 2px solid orange;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 0 12px rgba(255, 165, 0, 0.2);
  display: flex;
  flex-direction: column;
  gap: 15px;
  text-align: left;
}

.form-container h2 {
  color: #222;
  margin-bottom: 5px;
  font-size: 24px;
}

.form-intro {
  color: #555;
  font-size: 14px;
  margin-bottom: 20px;
}

.input-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon i {
  position: absolute;
  left: 12px;
  color: orange;
  font-size: 16px;
}

.input-icon input,
.input-icon textarea {
  width: 100%;
  padding: 12px 12px 12px 36px;
  font-size: 15px;
  border: 1px solid #ffa500;
  border-radius: 8px;
  box-sizing: border-box;
}

textarea {
  resize: none;
}

.phone-field {
  display: flex;
  gap: 10px;
  align-items: center;
}

.phone-field select {
  max-width: 30%;
  padding: 12px;
  font-size: 15px;
  border: 1px solid #ffa500;
  border-radius: 8px;
}

.phone-field .input-icon {
  flex: 1;
}

.form-container button {
  background-color: orange;
  color: white;
  font-weight: bold;
  font-size: 16px;
  padding: 12px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s;
}

.form-container button:hover {
  background-color: #e69500;
}

/* Responsive */
@media screen and (max-width: 600px) {
  .form-container {
    padding: 20px;
  }

  .phone-field {
    flex-direction: column;
    gap: 12px;
  }

  .phone-field select {
    max-width: 100%;
  }
}


/* Footer */


.footer {
  background:linear-gradient(to right, blue, white);
  color: black;
  text-align: center;
  padding: 2rem 1rem;
}

.footer-links {
  margin-bottom: 1rem;
}

.footer-links a {
  color: black;
  margin: 0 1rem;
  text-decoration: none;
}

@media screen and (max-width: 600px){
  .footer-links a{
    display: inline-block;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .menu-icon {
    display: flex;
  }
}

img {
  border-radius: 10px;
  text-align: center;
}

.step img.picard {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 15px;
}





/* Animations */
@keyframes slideDownAnim {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeUp 0.8s ease forwards;
}

.delay-1 {
  animation-delay: 0.2s;
}

.delay-2 {
  animation-delay: 0.4s;
}

.delay-3 {
  animation-delay: 0.6s;
}

.reveal {
  opacity: 0;
  transform: translateY(40px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.reveal.in-view {
  opacity: 1;
  transform: translateY(0);
}

.slide-up {
  opacity: 0;
  transform: translateY(40px);
  animation: fadeUp 1s ease forwards;
}


   