<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Skill Up – Learn a Skill</title>
  <link rel="stylesheet" href="anim.css" />
  <link rel="icon" type="image/png" href="images/ChatGPT Image May 5, 2025, 12_54_09 A">
<style>
/* Footer */
.footer {
  background: #a3fff4;
  color: black;
  text-align: center;
  padding: 2rem 1rem;
}
.footer-links {
  margin-bottom: 1rem;
}
.footer-links a {
  color: black;
  margin: 0 1rem;
  text-decoration: none;
}
@media screen and (max-width: 600px){
  .footer-links a{
    display: inline-block;
  }
}

.head {
text-align:center;
margin-bottom:25px;
background-color:#a3fff4;
width:auto;
border-radius:10px;
padding:10px;
margin-bottom:0rem;
}
.logo {
  color: white;
  font-size: 1.6rem;
  font-weight: bold;
height:6vh;
}
span{
color:black;
font-size:20px;
font-weight:bolder;
word-spacing:10px;
padding-bottom:10px;
}
a{
  text-decoration: none;
  color: black;
}




* {
            box-sizing: border-box;
        }

        body {
            background-color: #f1f1f1;
            font-family: Raleway, sans-serif;
            margin: 0;
            padding: 0;
        }

        #regForm {
            background: #fff;
            margin: 5px auto;
            padding: 10px;
            width: 90%;
            max-width: 800px;
            border-radius: 8px;
        }

        h1 {
            text-align: center;
            margin-bottom: 20px;
            display: block;
            font-family: Candara;
            font-size: 40px;
        }

        input,
        select,
        textarea {
            padding: 12px;
            width: 100%;
            font-size: 16px;
            margin-bottom: 20px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        input[type="radio"],
        input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }

        input.invalid {
            background-color: #ffdddd;
        }

        .tab {
            display: none;
        }

        button {
            background-color: #006644;
            color: white;
            border: none;
            padding: 12px 20px;
            font-size: 18px;
            cursor: pointer;
            border-radius: 4px;
            margin: 10px 5px;
        }

        button:hover {
            opacity: 0.9;
        }

        #prevBtn {
            background-color: #aaa;
        }

        .step {
            height: 15px;
            width: 15px;
            margin: 0 3px;
            background-color: #bbbbbb;
            border-radius: 50%;
            display: inline-block;
            opacity: 0.5;
            padding: 9px;
        }

        .step.active {
            opacity: 1;
        }

        .step.finish {
            background-color: #006644;
        }

        .row-flex {
            display: flex;
            gap: 10px;
        }

        .row-flex>* {
            flex: 1;
        }

        .rules a {
            color: blue;
            text-decoration: underline;
        }

        h2 {
            margin-bottom: 25px;
            border-bottom: 1px solid black;
            color: slategrey;
            text-align: center;
            font-family: Calibri;
            font-size: 28px;
        }

        @media screen and (max-width: 600px) {
            h1 {
                font-size: 35px;
            }

            h2 {
                font-size: 20px;
            }
        }

        a {
            text-decoration: none;
            color: black;
        }

        
  </style>
</head>
<body>
<header class="head">
    <div class="logos">
<img src="images/bglogo.png" alt="Eco Spark Logo" class="logo">

</div>
    <span>
      <a href="learner-acc.html">Learn</a>
      <a href="teacher.html">Teach</a>
      <a href="index.html">Grow</a>
    </span>
  </header>


  <form id="regForm" action="">
    <h1 class="slide-up">Learn Something New <br>
      <h2 class="slide-up" style="color: #006644;">Grow yourself by Learning</h2>
    </h1>
    <!-- One "tab" for each step in the form: -->
    <div class="tab slide-up">
      <label>Name</label>
      <input type="text" name="confirmName" id="">

      <label>Retype Password</label>
      <input type="password" id="myInput">
      <input type="checkbox" onclick="myFunction()">Show Password
    </div>



    <div class="tab slide-up">
      <label>What do you want to learn</label>
      <input name="skills" placeholder="e.g. Web Development" class="tag-input" required>

      <label>Preferred Teaching Method :</label> <br> <br>
      Online <input type="radio" name="method" value="Online" required> 
      In-Person <input type="radio" name="method" value="In-Person" required>
      Both <input type="radio" name="method" value="Both" required> <br>

      <label>Any Additional Info you want to add</label>
      <textarea name="bio" rows="4"></textarea>

    </div>


    <div class="tab slide-up">
      <label>Budget(₦)</label>
      <input type="text" name="budget" placeholder="₦10,000 - ₦20,000 / Free" required>

      <label>When Do You Want to Start?
          <p>You and Your Teacher will fix a Time and Date that fits you.</p>
      </label>
      <input type="datetime-local" name="start_date">

      <label>Teachers Location/City (Optional)</label>
      <select name="location">
        <option value="Abia - Umuahia">Abia - Umuahia</option>
        <option value="Adamawa - Yola">Adamawa - Yola</option>
        <option value="Akwa Ibom - Uyo">Akwa Ibom - Uyo</option>
        <option value="Anambra - Awka">Anambra - Awka</option>
        <option value="Bauchi - Bauchi">Bauchi - Bauchi</option>
        <option value="Bayelsa - Yenagoa">Bayelsa - Yenagoa</option>
        <option value="Benue - Makurdi">Benue - Makurdi</option>
        <option value="Borno - Maiduguri">Borno - Maiduguri</option>
        <option value="Cross River - Calabar">Cross River - Calabar</option>
        <option value="Delta - Asaba">Delta - Asaba</option>
        <option value="Ebonyi - Abakaliki">Ebonyi - Abakaliki</option>
        <option value="Edo - Benin City">Edo - Benin City</option>
        <option value="Ekiti - Ado Ekiti">Ekiti - Ado Ekiti</option>
        <option value="Enugu - Enugu">Enugu - Enugu</option>
        <option value="Gombe - Gombe">Gombe - Gombe</option>
        <option value="Imo - Owerri">Imo - Owerri</option>
        <option value="Jigawa - Dutse">Jigawa - Dutse</option>
        <option value="Kaduna - Kaduna">Kaduna - Kaduna</option>
        <option value="Kano - Kano">Kano - Kano</option>
        <option value="Katsina - Katsina">Katsina - Katsina</option>
        <option value="Kebbi - Birnin Kebbi">Kebbi - Birnin Kebbi</option>
        <option value="Kogi - Lokoja">Kogi - Lokoja</option>
        <option value="Kwara - Ilorin">Kwara - Ilorin</option>
        <option value="Lagos - Ikeja">Lagos - Ikeja</option>
        <option value="Nasarawa - Lafia">Nasarawa - Lafia</option>
        <option value="Niger - Minna">Niger - Minna</option>
        <option value="Ogun - Abeokuta">Ogun - Abeokuta</option>
        <option value="Ondo - Akure">Ondo - Akure</option>
        <option value="Osun - Osogbo">Osun - Osogbo</option>
        <option value="Oyo - Ibadan">Oyo - Ibadan</option>
        <option value="Plateau - Jos">Plateau - Jos</option>
        <option value="Rivers - Port Harcourt">Rivers - Port Harcourt</option>
        <option value="Sokoto - Sokoto">Sokoto - Sokoto</option>
        <option value="Taraba - Jalingo">Taraba - Jalingo</option>
        <option value="Yobe - Damaturu">Yobe - Damaturu</option>
        <option value="Zamfara - Gusau">Zamfara - Gusau</option>
        <option value="FCT - Abuja">FCT - Abuja</option>
      </select>


    </div>

    <div style="overflow:auto;">
      <div style="float:right;">
        <button type="button" id="prevBtn" onclick="nextPrev(-1)">Previous</button>
        <button type="button" id="nextBtn" onclick="nextPrev(1)">Next</button>
      </div>
    </div>



    <!-- Step Circles -->
    <div style="text-align:center;margin-top:30px;">
      <span class="step"></span>
      <span class="step"></span>
      <span class="step"></span>
      <span class="step"></span>
    </div>
  </form>


 <!-- FOOTER -->
 <footer class="footer">
  <div class="footer-links">
    <a href="index.html">Home</a>
    <a href="about.html">About</a>
    <a href="skills.html">Add a Skill</a>
    <a href="learn.html">Learn a Skill</a>
    <a href="contact.html">Contact</a>
  </div>
<p style="font-size: 1rem;">
            You’re viewing the <strong>Skill Up MVP (Minimum Viable Product)</strong>. We’re testing, improving, and
            building the full platform — and your feedback will shape the future of Skill Up.
        </p>
        <p>
       Got ideas or issues?<a href="contact.html" style="color: #111;">Tell us.</a> Your voice matters.
        </p>
  <p>&copy; 2025 Skill Up. All rights reserved.</p>
</footer>


<script>

  // Toggle map visibility
  function toggleMap(show) {
    document.getElementById('mapBox').style.display = show ? 'block' : 'none';
  }

  function myFunction() {
    var x = document.getElementById("myInput");
    if (x.type === "password") {
      x.type = "text";
    } else {
      x.type = "password";
    }
  }
  var currentTab = 0; // Current tab is set to be the first tab (0)
  showTab(currentTab); // Display the current tab

  function showTab(n) {
    // This function will display the specified tab of the form...
    var x = document.getElementsByClassName("tab");
    x[n].style.display = "block";
    //... and fix the Previous/Next buttons:
    if (n == 0) {
      document.getElementById("prevBtn").style.display = "none";
    } else {
      document.getElementById("prevBtn").style.display = "inline";
    }
    if (n == (x.length - 1)) {
      document.getElementById("nextBtn").innerHTML = "Submit";
    } else {
      document.getElementById("nextBtn").innerHTML = "Next";
    }
    //... and run a function that will display the correct step indicator:
    fixStepIndicator(n)
  }

  function nextPrev(n) {
    // This function will figure out which tab to display
    var x = document.getElementsByClassName("tab");
    // Exit the function if any field in the current tab is invalid:
    if (n == 1 && !validateForm()) return false;
    // Hide the current tab:
    x[currentTab].style.display = "none";
    // Increase or decrease the current tab by 1:
    currentTab = currentTab + n;
    // if you have reached the end of the form...
    if (currentTab >= x.length) {
      // ... the form gets submitted:
      document.getElementById("regForm").submit();
      return false;
    }
    // Otherwise, display the correct tab:
    showTab(currentTab);
  }

  function validateForm() {
    // This function deals with validation of the form fields
    var x, y, i, valid = true;
    x = document.getElementsByClassName("tab");
    y = x[currentTab].getElementsByTagName("input");
    // A loop that checks every input field in the current tab:
    for (i = 0; i < y.length; i++) {
      // If a field is empty...
      if (y[i].value == "") {
        // add an "invalid" class to the field:
        y[i].className += " invalid";
        // and set the current valid status to false
        valid = false;
      }
    }
    // If the valid status is true, mark the step as finished and valid:
    if (valid) {
      document.getElementsByClassName("step")[currentTab].className += " finish";
    }
    return valid; // return the valid status
  }

  function fixStepIndicator(n) {
    // This function removes the "active" class of all steps...
    var i, x = document.getElementsByClassName("step");
    for (i = 0; i < x.length; i++) {
      x[i].className = x[i].className.replace(" active", "");
    }
    //... and adds the "active" class on the current step:
    x[n].className += " active";
  }
</script>

</body>
</html>

