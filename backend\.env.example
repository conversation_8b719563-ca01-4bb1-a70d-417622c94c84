# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration (PlanetScale)
# Get these values from your PlanetScale dashboard
DATABASE_URL=mysql://username:<EMAIL>/database_name?sslaccept=strict
DB_HOST=host.planetscale.com
DB_USER=username
DB_PASSWORD=password
DB_NAME=database_name
DB_PORT=3306

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here-make-it-long-and-random
JWT_EXPIRE=1h
JWT_REFRESH_EXPIRE=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:5173

# Security
BCRYPT_SALT_ROUNDS=12

# Logging
LOG_LEVEL=info
